/* Dashboard Styles */
body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  background-color: #f9fafb;
}

.chart-container {
  height: 350px;
  margin-bottom: 1rem;
  background-color: #007B85;
  border-radius: 0.5rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.heatmap-cell {
  width: 1.5rem;
  height: 1.5rem;
  margin: 1px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  font-weight: 500;
  border: 1px solid #e5e7eb;
  color: white; /* All day numbers in white */
}

/* Larger heatmap cells on desktop */
@media (min-width: 1024px) {
  .heatmap-cell {
    width: 2rem;
    height: 2rem;
    margin: 2px;
    font-size: 0.75rem;
  }
}

/* New improved color scale */
.high {
  background-color: #166534; /* Dark green */
  color: white;
}

.good {
  background-color: #16a34a; /* Light green */
  color: white;
}

.medium {
  background-color: #fca5a5; /* Light red */
  color: white;
}

.low {
  background-color: #dc2626; /* Dark red */
  color: white;
}

.out-of-period {
  background-color: #f3f4f6; /* Light gray */
  color: white;
}

.future {
  background-color: #f0f0f0;
  color: #9ca3af;
}

.empty {
  background-color: transparent;
  border: none;
  color: #374151; /* Dark gray for headers */
}

.stat-card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid #e5e7eb;
}

.icon-circle {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.section-card {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  border: 1px solid #e5e7eb;
}

.section-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-content {
  padding: 1rem;
}

.map-container {
  height: 350px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
}

.map-svg {
  max-height: 100%;
  width: auto;
}

/* Chart responsive styles */
.recharts-wrapper {
  width: 100% !important;
}

/* Mobile chart stacking */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .chart-controls > div {
    margin-bottom: 0.5rem;
  }

  .chart-controls button {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}

/* Chart container improvements */
.chart-container-custom {
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.chart-controls-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}
